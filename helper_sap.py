import os
import traceback
import time
import win32com
import win32com.client
import pyperclip
from pathlib import Path
from utils.aws_helper import AWSHelper
#define global vars
gbl_start_time = time.time()
gbl_sap_session = None
 
# CONSTANTS
WND_0 = "wnd[0]"
BTN_PASSWORD = "wnd[0]/usr/pwdRSYST-BCODE"
BTN_TCODE = "wnd[0]/tbar[0]/okcd"
BTN_16 = "wnd[1]/tbar[0]/btn[16]"
BTN_24 = "wnd[1]/tbar[0]/btn[24]"
BTN_8 = "wnd[1]/tbar[0]/btn[8]"
BTN_0 = "wnd[1]/tbar[0]/btn[0]"
BTN_1 = "wnd[2]/usr/btnBUTTON_1"
BTN_LAYOUT = "wnd[0]/usr/ctxtP_VARI"
BTN_EXECUTE = "wnd[0]/tbar[1]/btn[8]"
BTN_MENU = "wnd[0]/mbar/menu[0]/menu[3]/menu[1]"
BTN_FILENAME = "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME"
BTN_20 = "wnd[1]/tbar[0]/btn[20]"
BTN_PATH = "wnd[1]/usr/ctxtDY_PATH"
BTN_USER_GROUP = "wnd[1]/usr/cntlGRID1/shellcont/shell"
BTN_USER_GROUP_1 = "wnd[0]/usr/cntlGRID_CONT0050/shellcont/shell"
BTN_PLANT = "wnd[0]/usr/btn%_SP$00003_%_APP_%-VALU_PUSH"
BTN_LAYOUT_1 = "wnd[0]/usr/ctxt%LAYOUT"
BTN_EXPORT = "wnd[0]/usr/cntlCONTAINER/shellcont/shell"
BTN_EXPORT_VALUE = "&MB_EXPORT"
from src.get_environment_task import BOT_NAME
from utils.logging_setup import get_logger
logger = get_logger(BOT_NAME)
 
class SAPHelper:
    def __init__(self, environment):
        self.environment = environment
        self.aws_helper = AWSHelper(BOT_NAME,environment=self.environment)
        self.sap_helper = None
 
 
    def launch_sap(self):
        aws_helper = AWSHelper(BOT_NAME, environment=self.environment)
        sap_env_path = aws_helper.retrieve_secret("EC01", "uc256-sap-server")
        sap_username = aws_helper.retrieve_secret("EC01", "uc256-sap-username")
        sap_password = aws_helper.retrieve_secret("EC01", "uc256-sap-password")
       
        global gbl_sap_session
        # Run taskkill and hide any messages (success or error)
        os.system("taskkill /F /IM saplgpad.exe >nul 2>nul")
       
        try:
            # Open SAP
            os.system(f'start {sap_env_path}')
            time.sleep(5)
 
            # Attempt to connect to SAP GUI
            try:
                sap_gui = win32com.client.GetObject("SAPGUI")
                application = sap_gui.GetScriptingEngine
            except Exception as com_err:
                raise RuntimeError("Failed to connect to SAP GUI. Ensure SAP GUI scripting is enabled.") from com_err
 
            # Validate the connection and session
            if not application.Children or len(application.Children) == 0:
                raise RuntimeError("No active SAP connections found.")
            connection = application.Children(0)
 
            if not connection.Children or len(connection.Children) == 0:
                raise RuntimeError("No active SAP sessions found.")
            gbl_sap_session = connection.Children(0)
 
            # Perform SAP login actions
            try:
                gbl_sap_session.findById(WND_0).maximize
                gbl_sap_session.findById("wnd[0]/usr/txtRSYST-BNAME").text = sap_username
               
                gbl_sap_session.findById(BTN_PASSWORD).text = sap_password
 
                gbl_sap_session.findById(BTN_PASSWORD).setFocus
                gbl_sap_session.findById(BTN_PASSWORD).caretPosition = len(sap_password)
                gbl_sap_session.findById(WND_0).sendVKey(0)
           
            except Exception as login_err:
                raise RuntimeError("Failed to perform SAP login actions.") from login_err
 
   
        except Exception as ex:
            str_exception = traceback.format_exc()
            raise RuntimeError("An unexpected error occurred while launching SAP.") from ex
       
   
    def connect_to_existing_sap(self):
        global gbl_sap_session
       
        try:
            # Connect to existing SAP GUI session
            sap_gui = win32com.client.GetObject("SAPGUI")
            application = sap_gui.GetScriptingEngine
           
            # Check if there are existing connections and sessions
            if not application.Children or len(application.Children) == 0:
                return False
               
            connection = application.Children(0)
            if not connection.Children or len(connection.Children) == 0:
                return False
               
            gbl_sap_session = connection.Children(0)
            print("Successfully connected to existing SAP session")
            return True
           
        except Exception as ex:
            print(f"Failed to connect to existing SAP session: {ex}")
            return False
    def navigate_to_tcode(self, tcode):
        gbl_sap_session.findById(WND_0).maximize()
        gbl_sap_session.findById(BTN_TCODE).text = tcode
        gbl_sap_session.findById(WND_0).sendVKey(0)
 
    def execute_zer0250(self, so_no):  
        #Select Layout
        gbl_sap_session.findById("wnd[0]/usr/ctxtP_LAYOUT").text = "/UC256_COC"
        gbl_sap_session.findById("wnd[0]/usr/ctxtP_LAYOUT").caretPosition = 9
 
        #Enter SO No
        gbl_sap_session.findById("wnd[0]/usr/ctxtP_SVBELN").text = so_no
        gbl_sap_session.findById("wnd[0]/usr/ctxtP_SVBELN").caretPosition = 3
               
 
        # #Execute
        gbl_sap_session.findById(BTN_EXECUTE).press()
 
    def export_zer0250(self, folder_path, file_name):
        print(f"folder: {folder_path}")
        gbl_sap_session.findById(BTN_MENU).select()
        gbl_sap_session.findById(BTN_FILENAME).text = file_name
        gbl_sap_session.findById(BTN_FILENAME).caretPosition = 8
        gbl_sap_session.findById(BTN_20).press()
        gbl_sap_session.findById(BTN_PATH).text = str(folder_path)
        gbl_sap_session.findById(BTN_PATH).setFocus()
        gbl_sap_session.findById(BTN_PATH).caretPosition = 100
        gbl_sap_session.findById(BTN_0).press()
 
        time.sleep(3)
       
        #Back to SAP Home page
        gbl_sap_session.findById(BTN_TCODE).text = "/n"
        gbl_sap_session.findById(WND_0).sendVKey(0)
        os.system("taskkill /F /IM excel.exe >nul 2>nul")
 
 
    def get_attachment_url(self, inspect_lot):
        gbl_sap_session.findById("wnd[0]/usr/ctxtQALS-PRUEFLOS").text = inspect_lot
        time.sleep(1)
        gbl_sap_session.findById("wnd[0]").sendVKey(0)
        time.sleep(1)

        # Press the GOS toolbox context button
        gbl_sap_session.findById("wnd[0]/titl/shellcont/shell").pressContextButton("%GOS_TOOLBOX")

        # Select "View Attachments" from context menu
        gbl_sap_session.findById("wnd[0]/titl/shellcont/shell").selectContextMenuItem("%GOS_VIEW_ATTA")

        # Find the row where 5th column contains "Internet Page"
        table = gbl_sap_session.findById("wnd[1]/usr/cntlCONTAINER_0100/shellcont/shell")
        target_row_index = None

        try:
            row_count = table.RowCount
            print(f"Table has {row_count} rows")

            for row_index in range(row_count):
                try:
                    # Get the 5th column value (index 4)
                    cell_value = table.GetCellValue(row_index, 4)
                    print(f"Row {row_index}, Column 5: {cell_value}")

                    if "Internet Page" in str(cell_value):
                        target_row_index = row_index
                        print(f"Found 'Internet Page' in row {row_index}")
                        break
                except Exception as e:
                    print(f"Error reading row {row_index}: {e}")
                    continue

            if target_row_index is None:
                print("No row found with 'Internet Page' in 5th column, using first row as fallback")
                target_row_index = 0

        except Exception as e:
            print(f"Error accessing table: {e}")
            print("Using first row as fallback")
            target_row_index = 0

        # Select the found row and press edit toolbar button
        table.selectedRows = str(target_row_index)
        table.pressToolbarButton("%ATTA_EDIT")
       
        # Wait for the URL dialog to appear
        time.sleep(2)
       
        # Get the URL address from the specific field
        try:
            url_address = gbl_sap_session.findById("wnd[1]/usr/txtSOS17-S_URL_KEY").text
           
            if url_address:
                pyperclip.copy(url_address)
                print(f"URL copied to clipboard: {url_address}")
                return url_address
            else:
                print("URL field is empty")
                return None
               
        except Exception as e:
            print(f"Error retrieving URL: {e}")
            return None
        finally:
            # Close the dialogs
            try:
                gbl_sap_session.findById("wnd[1]").close()
                gbl_sap_session.findById("wnd[1]/tbar[0]/btn[0]").press()
            except:
                pass